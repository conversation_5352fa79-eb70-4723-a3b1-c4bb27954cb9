import {
  createUniver,
  defaultTheme,
  LocaleType,
  merge,
} from "@univerjs/presets";
import { UniverSheetsCorePreset } from "@univerjs/presets/preset-sheets-core";
import UniverPresetSheetsCoreEnUS from "@univerjs/presets/preset-sheets-core/locales/en-US";

import "./style.css";
import "@univerjs/presets/lib/styles/preset-sheets-core.css";

import { BooleanNumber, SheetTypes } from "@univerjs/core";
import { LocaleType as CoreLocaleType } from "@univerjs/core";
import { useEffect, useRef, useState } from "react";
import { ToastContainer, Zoom } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

// Convert A-Z column letters to number index (0-based)
export const letterToColumn = (letter) => {
  let col = 0;
  for (let i = 0; i < letter.length; i++) {
    col = col * 26 + (letter.charCodeAt(i) - 65 + 1);
  }
  return col - 1;
};

// Convert number index to A-Z column letter
export const columnToLetter = (col) => {
  let letter = "";
  while (col >= 0) {
    letter = String.fromCharCode((col % 26) + 65) + letter;
    col = Math.floor(col / 26) - 1;
  }
  return letter;
};

// Convert "A1" -> { rowIndex, colIndex }
export const cellRefToIndices = (cellRef) => {
  const match = cellRef.match(/([A-Z]+)(\d+)/);
  if (!match) return null;
  const [, colLetter, rowNumber] = match;
  const rowIndex = parseInt(rowNumber, 10) - 1;
  const colIndex = letterToColumn(colLetter);
  return { rowIndex, colIndex };
};

export const transformApiToMatrix = (apiData) => {
  const cellData = {};
  let maxRow = 0;
  let maxCol = 0;

  if (apiData?.excelData) {
    Object?.entries(apiData.excelData).forEach(([cell, value]) => {
      const indices = cellRefToIndices(cell);
      if (!indices) return;
      const { rowIndex, colIndex } = indices;

      if (!cellData[rowIndex]) cellData[rowIndex] = {};

      if (typeof value === "string" && value.startsWith("=")) {
        cellData[rowIndex][colIndex] = { f: value };
      } else {
        cellData[rowIndex][colIndex] = { v: value };
      }

      maxRow = Math.max(maxRow, rowIndex);
      maxCol = Math.max(maxCol, colIndex);
    });
  }

  if (Array?.isArray(apiData?.maskedCells)) {
    apiData.maskedCells.forEach((cellRef) => {
      const indices = cellRefToIndices(cellRef);
      if (!indices) return;
      const { rowIndex, colIndex } = indices;

      if (!cellData[rowIndex]) cellData[rowIndex] = {};
      if (!cellData[rowIndex][colIndex]) cellData[rowIndex][colIndex] = {};

      cellData[rowIndex][colIndex].s = {
        bg: { rgb: "#FFFF00" },
        ht: 1,
      };

      if (apiData?.highlightAllMaskedCells) {
        cellData[rowIndex][colIndex].v = "";
      }
    });
  }

  return {
    id: `workbook-${Date.now()}`,
    name: "universheet",
    sheetOrder: ["sheet-01"],
    sheets: {
      "sheet-01": {
        type: SheetTypes.GRID,
        id: "sheet-01",
        name: "Sheet 1",
        cellData,
        rowCount: Math.max(maxRow + 1, 20),
        columnCount: Math.max(maxCol + 1, 20),
        defaultRowHeight: 28,
        defaultColumnWidth: 93,
      },
    },
  };
};

export const transformMatrixToApi = (cellData) => {
  const apiExcelData = {};
  if (!cellData) return { excelData: apiExcelData };

  Object.entries(cellData).forEach(([rowIndex, row]) => {
    Object.entries(row).forEach(([colIndex, cell]) => {
      const colLetter = columnToLetter(parseInt(colIndex));
      const rowNumber = parseInt(rowIndex) + 1;

      if (cell.f) {
        apiExcelData[`${colLetter}${rowNumber}`] = cell.f;
      } else if (cell.v !== undefined && cell.v !== null && cell.v !== "") {
        apiExcelData[`${colLetter}${rowNumber}`] = String(cell.v);
      }
    });
  });
  return { excelData: apiExcelData };
};

export default function ExcelSheets({
  cellsData,
  maskedCells,
  SetExcelApiData,
  SetCellsFilled,
  excelID,
  responseSubmitted,
  highlightAllMaskedCells = false,
}) {
  const [workbookData, setWorkbookData] = useState(null);
  const univerCreatedRef = useRef(false);
  const univerAPIRef = useRef(null);
  const workbookDataRef = useRef(null);

  useEffect(() => {
    const matrix = transformApiToMatrix({
      excelData: responseSubmitted || cellsData,
      maskedCells,
      highlightAllMaskedCells,
    });

    const initialWorkbook = {
      ...matrix,
      locale: CoreLocaleType.EN_US,
      appVersion: "3.0.0-alpha",
    };

    setWorkbookData(initialWorkbook);
    workbookDataRef.current = initialWorkbook;
    univerCreatedRef.current = false;
  }, [responseSubmitted, cellsData, maskedCells, highlightAllMaskedCells]);

  useEffect(() => {
    if (!workbookData || univerCreatedRef.current) return;

    const { univerAPI } = createUniver({
      locale: LocaleType.EN_US,
      locales: {
        [LocaleType.EN_US]: merge({}, UniverPresetSheetsCoreEnUS),
      },
      theme: defaultTheme,
      presets: [UniverSheetsCorePreset()],
    });

    univerAPIRef.current = univerAPI;
    univerAPI.createWorkbook(workbookData);

    const initialCellData = workbookData.sheets["sheet-01"].cellData;
    SetExcelApiData(transformMatrixToApi(initialCellData));

    // When highlightAllMaskedCells is true, we consider all cells as not filled
    // since we're hiding any data that might be in them
    if (highlightAllMaskedCells) {
      SetCellsFilled(false);
    } else {
      // Normal behavior - check if any masked cells have data
      let anyMaskedFilled = false;
      for (const cellRef of maskedCells) {
        const { rowIndex, colIndex } = cellRefToIndices(cellRef);
        const cell = initialCellData?.[rowIndex]?.[colIndex];
        if (cell?.v !== undefined && cell.v !== null && cell.v !== "") {
          anyMaskedFilled = true;
          break;
        }
      }
      SetCellsFilled(anyMaskedFilled);
    }

    // 🔒 Lock all cells except masked ones
    const restrictPermissionOnCells = async () => {
      const book = univerAPI?.getActiveWorkbook();
      const sheet = book.getActiveSheet();

      const bookId = book.getId();
      const sheetId = sheet.getSheetId();
      const permission = book.getPermission();

      const allCells = [];
      for (let row = 1; row <= 100; row++) {
        for (let col = 0; col < 26; col++) {
          const cell = `${String.fromCharCode(65 + col)}${row}`;
          allCells.push(cell);
        }
      }

      const cellsToLock = allCells.filter(
        (cell) => !maskedCells.includes(cell)
      );
      const ranges = cellsToLock.map((cell) => sheet.getRange(cell));

      const { permissionId, ruleId } = await permission.addRangeBaseProtection(
        bookId,
        sheetId,
        ranges
      );

      const rangeProtectionPermissionEditPoint =
        permission.permissionPointsDefinition
          .RangeProtectionPermissionEditPoint;

      permission.rangeRuleChangedAfterAuth$.subscribe((currentPermissionId) => {
        if (currentPermissionId === permissionId) {
          permission.setRangeProtectionPermissionPoint(
            bookId,
            sheetId,
            permissionId,
            rangeProtectionPermissionEditPoint,
            false
          );
        }
      });
    };

    restrictPermissionOnCells();

    // 🟡 Keep the existing edit logic for immediate UI updates
    univerAPI.addEvent(univerAPI.Event.SheetEditChanging, (params) => {
      const newValue = params?.value?._data?.body?.dataStream;
      const row = params?.row;
      const column = params?.column;

      if (newValue === undefined || row === undefined || column === undefined)
        return;

      const trimmedValue = String(newValue).trim();
      const refWorkbook = workbookDataRef.current;
      const cellData = refWorkbook?.sheets?.["sheet-01"]?.cellData || {};
      const updatedCellData = { ...cellData };

      if (!updatedCellData[row]) updatedCellData[row] = {};
      const existingCell = updatedCellData[row][column] || {};

      // Check if this is a masked cell
      const isMaskedCell = maskedCells.some((cellRef) => {
        const { rowIndex, colIndex } = cellRefToIndices(cellRef);
        return rowIndex === row && colIndex === column;
      });

      if (isMaskedCell) {
        // For masked cells
        if (highlightAllMaskedCells) {
          // When highlightAllMaskedCells is true:
          // 1. Always apply yellow highlighting
          // 2. Keep the cell value empty to hide any data
          updatedCellData[row][column] = {
            ...existingCell,
            v: "", // Keep the cell empty regardless of user input
            t: 1,
            s: {
              bg: { rgb: "#FFFF00" }, // Always highlight
              ht: 1, // 1 = left alignment
            },
          };
        } else {
          // When highlightAllMaskedCells is false:
          // 1. Always apply yellow highlighting
          // 2. Allow the cell to show user input (this will be updated by SheetValueChanged for formulas)
          updatedCellData[row][column] = {
            ...existingCell,
            v: trimmedValue,
            t: 1,
            s: {
              bg: { rgb: "#FFFF00" }, // Always highlight
              ht: 1, // 1 = left alignment
            },
          };
        }
      } else {
        // For non-masked cells, normal behavior
        updatedCellData[row][column] = {
          ...existingCell,
          v: trimmedValue,
          t: 1,
          s: existingCell?.s,
        };
      }

      // Only update workbookDataRef temporarily for UI responsiveness
      // The final state will be updated by SheetValueChanged event
      workbookDataRef.current = {
        ...refWorkbook,
        sheets: {
          ...refWorkbook.sheets,
          ["sheet-01"]: {
            ...refWorkbook.sheets["sheet-01"],
            cellData: updatedCellData,
          },
        },
      };
    });

    univerAPI.addEvent(univerAPI.Event.SheetValueChanged, () => {
      const book = univerAPI?.getActiveWorkbook();
      const sheet = book?.getActiveSheet();
      if (!sheet) return;

      const refWorkbook = workbookDataRef.current;
      const existingCellData =
        refWorkbook?.sheets?.["sheet-01"]?.cellData || {};
      const updatedCellData = {};

      // Get row and column counts from the sheet data instead of using non-existent methods
      const sheetData = sheet.getSheet().getSnapshot();
      const maxRow = sheetData.rowCount - 1;
      const maxCol = sheetData.columnCount - 1;

      for (let rowNum = 0; rowNum <= maxRow; rowNum++) {
        for (let colNum = 0; colNum <= maxCol; colNum++) {
          const range = sheet.getRange(rowNum, colNum);

          // Get both the formula and the calculated value from Univer's engine
          const formula = range.getFormula() || "";
          const calculatedValue = range.getValue() || "";
          const cellRef = `${columnToLetter(colNum)}${rowNum + 1}`;

          const existingCell = existingCellData?.[rowNum]?.[colNum];
          const isMaskedCell = maskedCells.includes(cellRef);

          // Process the cell only if it has a formula, a value, or is a masked cell that needs styling
          const hasData = formula || calculatedValue !== "" || isMaskedCell;

          if (hasData) {
            if (!updatedCellData[rowNum]) updatedCellData[rowNum] = {};

            const newCellData = {
              v: calculatedValue,
              t: 1, // Type TEXT
              s: existingCell?.s, // Preserve old styles if any
            };

            // Important: If there's a formula, store it in the 'f' property
            if (formula) {
              newCellData.f = formula;
            }

            if (isMaskedCell) {
              // Apply masked cell styling
              newCellData.s = {
                ...newCellData.s, // Keep other styles
                bg: { rgb: "#FFFF00" },
                ht: 1, // left alignment
              };
              if (highlightAllMaskedCells) {
                newCellData.v = ""; // Hide value if needed
              }
            }

            updatedCellData[rowNum][colNum] = newCellData;
          }
        }
      }

      // Update the reference with the complete data (values and formulas)
      workbookDataRef.current = {
        ...refWorkbook,
        sheets: {
          ...refWorkbook.sheets,
          ["sheet-01"]: {
            ...refWorkbook.sheets["sheet-01"],
            cellData: updatedCellData,
          },
        },
      };

      // Update the parent component's API data
      SetExcelApiData(transformMatrixToApi(updatedCellData));

      // Update cells filled status based on the new calculated values
      if (highlightAllMaskedCells) {
        SetCellsFilled(false);
      } else {
        let anyFilled = false;
        for (const cellRef of maskedCells) {
          const { rowIndex, colIndex } = cellRefToIndices(cellRef);
          const cell = updatedCellData?.[rowIndex]?.[colIndex];
          if (cell?.v !== undefined && cell.v !== null && cell.v !== "") {
            anyFilled = true;
            break;
          }
        }
        SetCellsFilled(anyFilled);
      }
    });

    univerCreatedRef.current = true;

    return () => {
      // This cleanup is crucial to destroy the old Univer instance
      // before creating a new one on subsequent renders (e.g., next question).
      univerAPIRef.current?.dispose?.();
      univerAPIRef.current = null;
      univerCreatedRef.current = false;
    };
  }, [
    workbookData,
    excelID,
    SetExcelApiData,
    SetCellsFilled,
    highlightAllMaskedCells,
    maskedCells,
  ]);

  return (
    <div>
      <div className="univer-container" id={excelID} />
      <ToastContainer transition={Zoom} />
    </div>
  );
}
